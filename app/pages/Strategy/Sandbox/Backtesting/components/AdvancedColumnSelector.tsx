import React, { useState, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ColumnStatistics, BucketConfig, ColumnBucketing } from '@/app/types';
import { ColumnStats } from './ColumnStats';
import { useBacktestingStore } from '@/app/store/backtesting/backtestingStore';

interface ColumnInfo {
  column: string;
  description?: string;
  statistics?: ColumnStatistics;
}

interface AdvancedColumnSelectorProps {
  selectedTable: string;
  selectedColumns: ColumnInfo[];
  onColumnsChange: (columns: ColumnInfo[]) => void;
  onColumnStatsRequest?: (column: string) => Promise<ColumnStatistics>;
}

// Helper function to generate default bucketing based on actual data
const generateDefaultBucketing = (
  columnType: 'numeric' | 'categorical' | 'datetime',
  allValues: string[],
  stats?: ColumnStatistics,
  minMaxValues?: { min_value: string; max_value: string } | null
): ColumnBucketing => {
  const buckets: BucketConfig[] = [];
  const totalValues = allValues.length;

  if (columnType === 'numeric' && allValues.length > 0) {
    // Convert string values to numbers and filter out invalid ones
    const numericValues = allValues
      .map(val => parseFloat(val))
      .filter(val => !isNaN(val))
      .sort((a, b) => a - b);

    if (numericValues.length === 0) {
      return { buckets: [], bucketingType: 'auto', totalCount: 0 };
    }

    const min = numericValues[0];
    const max = numericValues[numericValues.length - 1];
    const numBuckets = Math.min(5, Math.max(3, Math.ceil(Math.sqrt(numericValues.length))));
    const range = max - min;

    if (range === 0) {
      // All values are the same
      buckets.push({
        id: 'auto-0',
        label: `${min}`,
        type: 'auto',
        range: { min, max },
        count: numericValues.length,
        percentage: 100
      });
    } else {
      // Determine rounding unit based on range
      let roundingUnit: number;
      if (range >= 10000) roundingUnit = 1000;
      else if (range >= 1000) roundingUnit = 100;
      else if (range >= 100) roundingUnit = 10;
      else if (range >= 10) roundingUnit = 5;
      else if (range >= 1) roundingUnit = 1;
      else roundingUnit = 0.1;

      const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
      const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;
      const bucketSize = (roundedMax - roundedMin) / numBuckets;

      // Create buckets and count actual values in each bucket
      for (let i = 0; i < numBuckets; i++) {
        const bucketMin = roundedMin + i * bucketSize;
        const bucketMax = i === numBuckets - 1 ? roundedMax : roundedMin + (i + 1) * bucketSize;

        // Count values in this bucket
        const valuesInBucket = numericValues.filter(val =>
          val >= bucketMin && (i === numBuckets - 1 ? val <= bucketMax : val < bucketMax)
        );

        buckets.push({
          id: `auto-${i}`,
          label: `${bucketMin.toFixed(roundingUnit < 1 ? 1 : 0)} - ${bucketMax.toFixed(roundingUnit < 1 ? 1 : 0)}`,
          type: 'auto',
          range: { min: bucketMin, max: bucketMax },
          count: valuesInBucket.length,
          percentage: totalValues > 0 ? (valuesInBucket.length / totalValues) * 100 : 0
        });
      }
    }
  } else if (columnType === 'categorical' && allValues.length > 0) {
    // Count occurrences of each value
    const valueCounts = allValues.reduce((acc, value) => {
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Sort by count (descending) and create buckets
    const sortedValues = Object.entries(valueCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20); // Limit to top 20 values to avoid too many buckets

    sortedValues.forEach(([value, count], i) => {
      buckets.push({
        id: `auto-${i}`,
        label: value,
        type: 'auto',
        values: [value],
        count,
        percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
      });
    });
  } else if (columnType === 'datetime' && allValues.length > 0) {
    // Parse datetime values and create time-based buckets
    const dateValues = allValues
      .map(val => new Date(val))
      .filter(date => !isNaN(date.getTime()))
      .sort((a, b) => a.getTime() - b.getTime());

    if (dateValues.length === 0) {
      return { buckets: [], bucketingType: 'auto', totalCount: 0 };
    }

    const minDate = dateValues[0];
    const maxDate = dateValues[dateValues.length - 1];
    const timeDiff = maxDate.getTime() - minDate.getTime();
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

    // Determine appropriate time bucketing based on data range
    let bucketType: 'hour' | 'day' | 'week' | 'month' | 'year';
    if (daysDiff <= 1) bucketType = 'hour';
    else if (daysDiff <= 31) bucketType = 'day';
    else if (daysDiff <= 90) bucketType = 'week';
    else if (daysDiff <= 730) bucketType = 'month';
    else bucketType = 'year';

    // Group dates by the determined bucket type and track date ranges
    const dateBuckets = dateValues.reduce((acc, date) => {
      let key: string;
      let startDate: Date;
      let endDate: Date;

      switch (bucketType) {
        case 'hour':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
          startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), 0, 0);
          endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), 59, 59);
          break;
        case 'day':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
          endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          weekStart.setHours(0, 0, 0, 0);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          weekEnd.setHours(23, 59, 59, 999);
          key = `Week of ${weekStart.getFullYear()}-${String(weekStart.getMonth() + 1).padStart(2, '0')}-${String(weekStart.getDate()).padStart(2, '0')}`;
          startDate = weekStart;
          endDate = weekEnd;
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          startDate = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0);
          endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59);
          break;
        case 'year':
          key = `${date.getFullYear()}`;
          startDate = new Date(date.getFullYear(), 0, 1, 0, 0, 0);
          endDate = new Date(date.getFullYear(), 11, 31, 23, 59, 59);
          break;
      }

      if (!acc[key]) {
        acc[key] = { count: 0, startDate, endDate };
      }
      acc[key].count += 1;
      return acc;
    }, {} as Record<string, { count: number; startDate: Date; endDate: Date }>);

    // Create buckets from grouped dates with dateRange
    Object.entries(dateBuckets)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([period, { count, startDate, endDate }], i) => {
        buckets.push({
          id: `auto-${i}`,
          label: period,
          type: 'auto',
          dateRange: {
            start: startDate.toISOString().slice(0, 16), // Format for datetime-local input
            end: endDate.toISOString().slice(0, 16)
          },
          count,
          percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
        });
      });
  }

  return {
    buckets,
    bucketingType: 'auto',
    totalCount: totalValues
  };
};

export const AdvancedColumnSelector: React.FC<AdvancedColumnSelectorProps> = ({
  selectedTable,
  selectedColumns,
  onColumnsChange,
  onColumnStatsRequest
}) => {
  const [loadingStats, setLoadingStats] = useState<Record<string, boolean>>({});
  const [loadingBucketCounts, setLoadingBucketCounts] = useState<Record<string, boolean>>({});
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const { tables } = useBacktestingStore();
  const selectedTableData = tables.find(t => t.id === selectedTable);
  const availableColumns = selectedTableData
    ? Object.keys(selectedTableData.schema.shape).filter(col =>
        !selectedColumns.some(selected => selected.column === col)
      )
    : [];

  const handleColumnSelect = useCallback((column: string) => {
    setSelectedColumn(column);
  }, []);

  const handleAddColumn = useCallback(async () => {
    if (!selectedColumn || !selectedTableData) return;

    // Check if we've reached the maximum of 3 columns
    if (selectedColumns.length >= 3) {
      alert('Maximum of 3 metric columns allowed');
      return;
    }

    const description = (selectedTableData.schema.shape as any)[selectedColumn]?._def.description;
    const columnType = (selectedTableData.schema.shape as any)[selectedColumn]?._def.typeName;

    setLoadingStats(prev => ({ ...prev, [selectedColumn]: true }));

    try {
      let stats: ColumnStatistics | undefined;

      // Fetch column statistics if available
      if (onColumnStatsRequest) {
        stats = await onColumnStatsRequest(selectedColumn);
      }

      // Determine column type for bucketing
      let detectedType: 'numeric' | 'categorical' | 'datetime' = 'categorical';
      if (columnType === 'ZodNumber') {
        detectedType = 'numeric';
      } else if (columnType === 'ZodDate') {
        detectedType = 'datetime';
      }

      // Fetch all values for bucketing - always get all values for proper bucketing
      const { fetchFieldValues } = useBacktestingStore.getState();
      let allValues: string[] = [];
      let minMaxValues: { min_value: string; max_value: string } | null = null;

      try {
        // Always fetch all distinct values for proper bucketing
        const allResponse = await fetchFieldValues(selectedTable, selectedColumn, 'all');
        if ('distinct_values' in allResponse) {
          allValues = allResponse.distinct_values;
        }

        // For numeric and datetime, also get min/max for range information
        if (detectedType === 'numeric' || detectedType === 'datetime') {
          const minMaxResponse = await fetchFieldValues(selectedTable, selectedColumn, 'min_max');
          if ('min_value' in minMaxResponse && 'max_value' in minMaxResponse) {
            minMaxValues = {
              min_value: minMaxResponse.min_value,
              max_value: minMaxResponse.max_value
            };
          }
        }
      } catch (error) {
        console.error('Failed to fetch field values:', error);
      }

      // Generate default bucketing
      const defaultBucketing = generateDefaultBucketing(detectedType, allValues, stats, minMaxValues);

      // Create enhanced statistics with bucketing data
      const enhancedStats: ColumnStatistics = {
        ...stats,
        type: detectedType,
        allValues,
        bucketing: defaultBucketing
      } as ColumnStatistics;

      const newColumn: ColumnInfo = {
        column: selectedColumn,
        description,
        statistics: enhancedStats
      };

      // Add column to UI immediately for responsiveness
      const updatedColumns = [...selectedColumns, newColumn];
      onColumnsChange(updatedColumns);
      setSelectedColumn(''); // Reset selection after adding

      // Call API to get accurate bucket counts
      if (defaultBucketing.buckets.length > 0) {
        try {
          setLoadingBucketCounts(prev => ({ ...prev, [selectedColumn]: true }));
          const { calculateBucketCounts } = useBacktestingStore.getState();
          const response = await calculateBucketCounts(selectedTable, selectedColumn, defaultBucketing.buckets);

          // Update the column with accurate counts
          const finalBucketing = {
            ...defaultBucketing,
            buckets: response.buckets,
            totalCount: response.total_count
          };

          const finalStats: ColumnStatistics = {
            ...enhancedStats,
            bucketing: finalBucketing
          };

          const finalColumn: ColumnInfo = {
            ...newColumn,
            statistics: finalStats
          };

          // Update with accurate counts
          const finalColumns = updatedColumns.map(col =>
            col.column === selectedColumn ? finalColumn : col
          );
          onColumnsChange(finalColumns);
        } catch (error) {
          console.error('Failed to calculate initial bucket counts:', error);
          // Keep the column with zero counts if API fails
        } finally {
          setLoadingBucketCounts(prev => ({ ...prev, [selectedColumn]: false }));
        }
      }

    } catch (error) {
      console.error('Failed to add column:', error);
    } finally {
      setLoadingStats(prev => ({ ...prev, [selectedColumn]: false }));
    }
  }, [selectedColumn, selectedColumns, onColumnsChange, onColumnStatsRequest, selectedTableData, selectedTable]);

  const handleRemoveColumn = useCallback((column: string) => {
    onColumnsChange(selectedColumns.filter(col => col.column !== column));
  }, [selectedColumns, onColumnsChange]);

  const handleBucketingUpdate = useCallback((column: string, bucketing: ColumnBucketing) => {
    const updatedColumns = selectedColumns.map(col =>
      col.column === column
        ? {
            ...col,
            statistics: col.statistics ? { ...col.statistics, bucketing } : undefined
          }
        : col
    );
    onColumnsChange(updatedColumns);
  }, [selectedColumns, onColumnsChange]);

  return (
    <div className="space-y-4">
      {/* Header with column count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Selected: {selectedColumns.length}/3 columns
        </div>
        {selectedColumns.length >= 3 && (
          <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
            Maximum limit reached
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Select
          value={selectedColumn}
          onValueChange={handleColumnSelect}
          disabled={availableColumns.length === 0 || selectedColumns.length >= 3}
        >
          <SelectTrigger className="w-full">
            <SelectValue>
              {selectedColumn ? (
                <div className="flex items-center">
                  <span className="truncate">{selectedColumn}</span>
                  {selectedTableData?.schema.shape &&
                   selectedColumn in selectedTableData.schema.shape && (
                    <span className="ml-2 text-sm text-muted-foreground truncate">
                      ({(selectedTableData.schema.shape as any)[selectedColumn]?._def.description || ''})
                    </span>
                  )}
                </div>
              ) : selectedColumns.length >= 3 ? (
                "Maximum 3 columns selected"
              ) : (
                "Select a column to analyze"
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {availableColumns.map(column => (
              <SelectItem key={column} value={column}>
                <div className="space-y-1">
                  <div className="font-medium">{column}</div>
                  <div className="text-sm text-muted-foreground">
                    {(selectedTableData?.schema.shape as any)[column]?._def.description || ''}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="icon"
          onClick={handleAddColumn}
          disabled={!selectedColumn || selectedColumns.length >= 3}
          title={selectedColumns.length >= 3 ? "Maximum 3 columns allowed" : "Add column"}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-2">
        {selectedColumns.map(({ column, description, statistics }) => (
          <ColumnStats
            key={column}
            tableId={selectedTable}
            column={column}
            description={description}
            stats={statistics}
            isLoading={loadingStats[column] || loadingBucketCounts[column]}
            onRemove={() => handleRemoveColumn(column)}
            onBucketingUpdate={(bucketing) => handleBucketingUpdate(column, bucketing)}
          />
        ))}
      </div>
    </div>
  );
};