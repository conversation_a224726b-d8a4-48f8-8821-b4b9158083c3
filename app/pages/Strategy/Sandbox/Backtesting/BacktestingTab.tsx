import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Database, Settings, Filter, Save, <PERSON><PERSON><PERSON>, ArrowR<PERSON>, Wand2 } from 'lucide-react';
import { BacktestingConfig, ColumnConfig, TimeframeFilter, PopulationFilter, availableTables } from '../SampleSandboxData';
import { DataTableSelector, ConfigSection, FiltersSection, AnalysisSection, AnalysisArtifact } from './components';
import { Section, SectionButtonConfig, SectionButtonType } from './components/SectionHeader';
import { MetricEquation, BaseMetricCondition, ColumnStatistics } from '@/app/types';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { BacktestingAnalysisSettings } from './types';
import type { AnalysisData, RuleAnalysisData, MetricAnalysisData } from './components/AnalysisArtifact';
import { SQLEditorArtifact } from '@/components/custom/SQLEditorArtifact';
import { useBacktestingStore } from '@/app/store/backtesting/backtestingStore';

type SectionKey = 'dataTable' | 'config' | 'filters' | 'analysis';
type AutoDetectType = 'config' | 'filters' | null;

interface SectionState {
  dataTable: boolean;
  config: boolean;
  filters: boolean;
  analysis: boolean;
}

interface UnsavedChangesState extends SectionState {}

const INITIAL_SECTION_STATE: SectionState = {
  dataTable: false,
  config: false,
  filters: false,
  analysis: false
};

const INITIAL_CONFIG: BacktestingConfig = {
  selectedTable: 'transactions',
  columnConfig: {
    rowUniqueId: '',
    datetime: '',
    fraudLabel: '',
    amount: '',
  },
  timeframeFilter: {
    startDate: '',
    endDate: ''
  },
  populationFilters: []
};

type MetricColumn = NonNullable<BacktestingAnalysisSettings['analysis']['metric']>['selectedColumns'][number];

export default function BacktestingTab() {
  const [expandedSections, setExpandedSections] = useState<SectionState>(INITIAL_SECTION_STATE);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<UnsavedChangesState>(INITIAL_SECTION_STATE);
  const [config, setConfig] = useState<BacktestingConfig>(INITIAL_CONFIG);
  const [analysisType, setAnalysisType] = useState<'rule' | 'metric'>('rule');
  const [ruleAnalysis, setRuleAnalysis] = useState<MetricEquation>({ operator: 'AND', conditions: [] });
  const [metricAnalysis, setMetricAnalysis] = useState<MetricColumn[]>([]);
  const [isTimeframeValid, setIsTimeframeValid] = useState(true);
  const [pendingAutoDetect, setPendingAutoDetect] = useState<AutoDetectType>(null);
  const { addTab, setCollapsed } = useArtifactStore();
  const { tables, fetchTables, getColumnConfig, runRuleAnalysis } = useBacktestingStore();

  // Refs
  const isInitialRender = useRef(true);
  const configAutoDetectRef = useRef<(() => void) | null>(null);
  const filtersAutoDetectRef = useRef<(() => void) | null>(null);

  // Update columnConfig when selectedTable changes
  useEffect(() => {
    if (config.selectedTable) {
      const newColumnConfig = getColumnConfig(config.selectedTable);
      setConfig(prev => ({ ...prev, columnConfig: newColumnConfig }));
    }
  }, [config.selectedTable, getColumnConfig]);

  // Previous state for change detection
  const [prevConfig, setPrevConfig] = useState(config);
  const [prevRuleAnalysis, setPrevRuleAnalysis] = useState(ruleAnalysis);
  const [prevMetricAnalysis, setPrevMetricAnalysis] = useState<MetricColumn[]>([]);
  const [prevAnalysisType, setPrevAnalysisType] = useState<'rule' | 'metric'>('rule');

  // Fetch tables on mount
  useEffect(() => {
    fetchTables();
  }, [fetchTables]);

  // Replace availableTables with tables from store
  const selectedTableData = tables.find(t => t.id === config.selectedTable);

  // Effect to detect config changes
  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    const columnConfigChanged = JSON.stringify(config.columnConfig) !== JSON.stringify(prevConfig.columnConfig);
    const filtersChanged = JSON.stringify({
      timeframeFilter: config.timeframeFilter,
      populationFilters: config.populationFilters
    }) !== JSON.stringify({
      timeframeFilter: prevConfig.timeframeFilter,
      populationFilters: prevConfig.populationFilters
    });
    const dataTableChanged = config.selectedTable !== prevConfig.selectedTable;

    if (columnConfigChanged || filtersChanged || dataTableChanged) {
      setHasUnsavedChanges(prev => ({
        ...prev,
        config: columnConfigChanged,
        filters: filtersChanged,
        dataTable: dataTableChanged
      }));
      setPrevConfig(config);
    }
  }, [config, prevConfig]);

  // Effect to detect analysis changes
  useEffect(() => {
    if (isInitialRender.current) return;

    const hasRuleChanges = analysisType === 'rule' &&
      JSON.stringify(ruleAnalysis) !== JSON.stringify(prevRuleAnalysis);
    const hasMetricChanges = analysisType === 'metric' &&
      JSON.stringify(metricAnalysis) !== JSON.stringify(prevMetricAnalysis);
    const hasTypeChanges = analysisType !== prevAnalysisType;

    if (hasRuleChanges || hasMetricChanges || hasTypeChanges) {
      setHasUnsavedChanges(prev => ({ ...prev, analysis: true }));
      if (analysisType === 'rule') {
        setPrevRuleAnalysis(ruleAnalysis);
      } else {
        setPrevMetricAnalysis(metricAnalysis);
      }
      setPrevAnalysisType(analysisType);
    }
  }, [ruleAnalysis, metricAnalysis, prevRuleAnalysis, prevMetricAnalysis, analysisType, prevAnalysisType]);

  const handleSave = useCallback((section: SectionKey) => {
    // Update previous values based on section
    switch (section) {
      case 'config':
        setPrevConfig(prev => ({ ...prev, columnConfig: config.columnConfig }));
        break;
      case 'filters':
        setPrevConfig(prev => ({
          ...prev,
          timeframeFilter: config.timeframeFilter,
          populationFilters: config.populationFilters
        }));
        setExpandedSections(prev => ({ ...prev, analysis: true, filters: false }));
        break;
      case 'dataTable':
        // When data table changes are saved, reset other sections
        // Update prevConfig with all reset values
        setPrevConfig({
          selectedTable: config.selectedTable,
          columnConfig: {
            rowUniqueId: '',
            datetime: '',
            fraudLabel: '',
            amount: '',
          },
          timeframeFilter: {
            startDate: '',
            endDate: ''
          },
          populationFilters: []
        });
        // Reset config section
        setConfig(prev => ({
          ...prev,
          columnConfig: {
            rowUniqueId: '',
            datetime: '',
            fraudLabel: '',
            amount: '',
          }
        }));
        // Reset filters section
        setConfig(prev => ({
          ...prev,
          timeframeFilter: {
            startDate: '',
            endDate: ''
          },
          populationFilters: []
        }));
        // Reset analysis section
        setRuleAnalysis({ operator: 'AND', conditions: [] });
        setMetricAnalysis([]);
        setAnalysisType('rule');
        // Reset unsaved changes for all sections except dataTable
        setHasUnsavedChanges(prev => ({
          ...prev,
          config: false,
          filters: false,
          analysis: false
        }));
        break;
      case 'analysis':
        setPrevRuleAnalysis(ruleAnalysis);
        setPrevMetricAnalysis(metricAnalysis);
        setPrevAnalysisType(analysisType);
        break;
    }

    // Always set hasUnsavedChanges to false for the section being saved
    setHasUnsavedChanges(prev => ({ ...prev, [section]: false }));

    if (section !== 'filters') {
      setExpandedSections(prev => ({ ...prev, [section]: false }));
    }
  }, [config, prevConfig, ruleAnalysis, prevRuleAnalysis, metricAnalysis, prevMetricAnalysis, analysisType, prevAnalysisType]);

  const toggleSection = useCallback((section: SectionKey) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  }, []);

  const getSectionWarnings = useCallback((section: SectionKey): string[] => {
    const warnings: string[] = [];

    switch (section) {
      case 'dataTable':
        if (!config.selectedTable) {
          warnings.push('Please select a data table to proceed');
        }
        break;
      case 'config':
        const requiredFields = ['rowUniqueId', 'datetime', 'fraudLabel', 'amount'];
        const missingFields = requiredFields.filter(field => !config.columnConfig[field as keyof typeof config.columnConfig]);
        if (missingFields.length > 0) {
          warnings.push('Please map all required fields to proceed');
        }
        if (!config.selectedTable) {
          warnings.push('Please select a data table in the Data Table section to enable column mapping');
        }
        break;
      case 'filters':
        if (!config.columnConfig.datetime) {
          warnings.push('Please select a datetime column in the Configuration section to enable timeframe filtering');
        } else if (!config.timeframeFilter.startDate || !config.timeframeFilter.endDate) {
          warnings.push('Please provide both start and end dates for the timeframe filter');
        }
        break;
      case 'analysis':
        const otherSectionsHaveChanges = hasUnsavedChanges.dataTable ||
                                       hasUnsavedChanges.config ||
                                       hasUnsavedChanges.filters;
        const otherSectionsHaveWarnings = getSectionWarnings('dataTable').length > 0 ||
                                        getSectionWarnings('config').length > 0 ||
                                        getSectionWarnings('filters').length > 0;

        if (otherSectionsHaveChanges) {
          warnings.push('Please save changes in other sections before running analysis');
        }
        if (otherSectionsHaveWarnings) {
          warnings.push('Please resolve warnings in other sections before running analysis');
        }
        if (analysisType === 'rule' && ruleAnalysis.conditions.length === 0) {
          warnings.push('Please add at least one condition to proceed with rule analysis');
        }
        if (analysisType === 'metric' && metricAnalysis.length === 0) {
          warnings.push('Please select at least one metric column to proceed with metric analysis');
        }
        break;
    }

    return warnings;
  }, [config, hasUnsavedChanges, analysisType, ruleAnalysis, metricAnalysis]);

  const handleRunAnalysis = useCallback(async (section: SectionKey) => {
    if (section !== 'analysis') return;

    // Save changes first
    handleSave(section);

    const analysisId = `analysis-${Date.now()}`;

    const analysisSettings: BacktestingAnalysisSettings = {
      analysisId,
      analysisType,
      timestamp: new Date().toISOString(),
      dataTable: {
        selectedTable: config.selectedTable,
        columnConfig: config.columnConfig,
        tableDescription: selectedTableData?.description
      },
      configuration: {
        columnMappings: {
          rowUniqueId: {
            mappedTo: config.columnConfig.rowUniqueId,
            description: 'Unique identifier for each row in the dataset'
          },
          datetime: {
            mappedTo: config.columnConfig.datetime,
            description: 'Timestamp of the event (transaction/application)'
          },
          fraudLabel: {
            mappedTo: config.columnConfig.fraudLabel,
            description: 'Boolean flag indicating if the event is fraudulent'
          },
          amount: {
            mappedTo: config.columnConfig.amount,
            description: 'Monetary value of the transaction or application'
          },
          ...(config.columnConfig.applicationId && {
            applicationId: {
              mappedTo: config.columnConfig.applicationId,
              description: 'Unique identifier for the application'
            }
          }),
          ...(config.columnConfig.accountId && {
            accountId: {
              mappedTo: config.columnConfig.accountId,
              description: 'Unique identifier for the account'
            }
          }),
          ...(config.columnConfig.transactionId && {
            transactionId: {
              mappedTo: config.columnConfig.transactionId,
              description: 'Unique identifier for the transaction'
            }
          })
        },
        autoDetected: false
      },
      filters: {
        timeframeFilter: config.timeframeFilter,
        populationFilters: config.populationFilters,
        timeframeAutoDetected: false
      },
      analysis: {
        type: analysisType,
        ...(analysisType === 'rule'
          ? {
              rule: {
                equation: ruleAnalysis,
                operator: ruleAnalysis.operator as 'AND' | 'OR',
                conditions: ruleAnalysis.conditions
                  .filter((condition): condition is BaseMetricCondition => 'metric_name' in condition)
                  .map(condition => ({
                    metric_name: condition.metric_name,
                    operation: condition.operation,
                    value: condition.value,
                    type: condition.type as 'string' | 'boolean' | 'numeric'
                  }))
              }
            }
          : {
              metric: {
                selectedColumns: metricAnalysis.map(({ column, description, statistics }) => ({
                  column,
                  description,
                  statistics
                }))
              }
            }
        )
      }
    };

    try {
      if (analysisType === 'rule') {
        await runRuleAnalysis(analysisSettings);
      }

      const analysisData: AnalysisData = {
        type: analysisType,
        data: analysisType === 'rule'
          ? {
              rule: {
                metric_equation: ruleAnalysis,
                rule_code: analysisId,
                created_at: new Date().toISOString(),
                created_by: '<EMAIL>',
                rule_status: true,
                rule_severity: 'Medium',
                version: 1
              }
            } as RuleAnalysisData
          : {
              columns: metricAnalysis.map(({ column, description }) => ({ column, description })),
              timeframe: config.timeframeFilter,
              populationFilters: config.populationFilters,
              analysisId
            } as MetricAnalysisData,
        settings: analysisSettings
      };

      // Add the tab with dynamic title based on analysis type
      addTab({
        id: analysisId,
        title: `${analysisType === 'rule' ? 'Rule' : 'Metric'} Analysis - ${new Date().toLocaleTimeString()}`,
        renderArtifact: () => <AnalysisArtifact analysis={analysisData} />
      });

      // Force activate the tab to ensure it's visible
      setTimeout(() => {
        const store = useArtifactStore.getState();
        store.forceActivateTab(analysisId);
        store.setCollapsed(false);
      }, 0);
    } catch (error) {
      console.error('Error running analysis:', error);
      // TODO: Show error to user
    }
  }, [analysisType, ruleAnalysis, metricAnalysis, config, handleSave, addTab, selectedTableData, runRuleAnalysis]);

  const getButtonConfigs = useCallback((section: SectionKey): SectionButtonConfig[] => {
    const warnings = getSectionWarnings(section);
    const hasWarnings = warnings.length > 0;
    const baseConfigs: SectionButtonConfig[] = [];

    // Add save button for all sections except analysis
    if (section !== "analysis") {
      baseConfigs.push({
        icon: Save,
        text: "Save Changes",
        type: 'save',
        disabled: !hasUnsavedChanges[section],
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          handleSave(section);
        }
      });
    }

    // Add run button for analysis section with dynamic text based on analysis type
    if (section === "analysis") {
      baseConfigs.push({
        icon: LineChart,
        text: analysisType === 'rule' ? "Run Rule Analysis" : "Run Metric Analysis",
        type: 'run',
        disabled: getSectionWarnings(section).length > 0,
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          handleRunAnalysis(section);
        }
      });

      // Add create custom metric button
      baseConfigs.push({
        icon: Database,
        text: "Create Custom Metric",
        type: 'create-custom-metric',
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          const sqlEditorId = `sql-editor-${Date.now()}`;
          addTab({
            id: sqlEditorId,
            title: `SQL Editor - ${new Date().toLocaleTimeString()}`,
            renderArtifact: () => <SQLEditorArtifact />
          });
          setCollapsed(false);
        }
      });
    }

    // Add auto-detect buttons for specific sections
    if (section === "config") {
      baseConfigs.push({
        icon: Wand2,
        text: "Auto-Detect Columns",
        type: 'autodetect-columns',
        disabled: !config.selectedTable,
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          if (configAutoDetectRef.current && config.selectedTable) {
            configAutoDetectRef.current();
          }
        }
      });
    } else if (section === "filters") {
      baseConfigs.push({
        icon: Wand2,
        text: "Auto-Detect Timeframe",
        type: 'autodetect-timeframe',
        disabled: !config.columnConfig.datetime,
        onClick: (e: React.MouseEvent) => {
          e.stopPropagation();
          if (filtersAutoDetectRef.current && config.columnConfig.datetime) {
            filtersAutoDetectRef.current();
          }
        }
      });
    }

    return baseConfigs;
  }, [hasUnsavedChanges, handleSave, handleRunAnalysis, getSectionWarnings, config, expandedSections, analysisType]);

  const handleExpandComplete = useCallback((section: SectionKey) => {
    if (pendingAutoDetect === section) {
      const autoDetectFn = section === 'config' ? configAutoDetectRef.current : filtersAutoDetectRef.current;
      if (autoDetectFn) {
        const fn = autoDetectFn;
        setTimeout(() => setPendingAutoDetect(null), 100);
        fn();
      }
    }
  }, [pendingAutoDetect]);

  // Add a function to fetch column statistics
  const handleColumnStatsRequest = useCallback(async (column: string): Promise<ColumnStatistics> => {
    // In a real application, this would fetch statistics from an API
    // For now, we'll simulate some statistics
    const selectedTableData = tables.find(t => t.id === config.selectedTable);
    if (!selectedTableData) {
      throw new Error('Table not found');
    }

    const columnType = (selectedTableData.schema.shape as any)[column]?._def.typeName;
    const isNumeric = columnType === 'ZodNumber';
    const isBoolean = columnType === 'ZodBoolean';

    // Simulate some statistics
    if (isNumeric) {
      return {
        type: 'numeric',
        nullPercentage: 0.05,
        numericStats: {
          min: Math.random() * 100,
          max: Math.random() * 1000 + 100,
          avg: Math.random() * 500 + 50
        }
      };
    } else if (isBoolean) {
      return {
        type: 'categorical',
        nullPercentage: 0.02,
        categoricalStats: {
          valueCounts: [
            { value: 'true', count: 750, percentage: 0.75 },
            { value: 'false', count: 250, percentage: 0.25 }
          ]
        }
      };
    } else {
      // For string columns, simulate some categorical data
      const categories = ['A', 'B', 'C', 'D', 'E'];
      const total = 1000;
      const counts = categories.map(() => Math.floor(Math.random() * 200) + 100);
      const sum = counts.reduce((a, b) => a + b, 0);

      return {
        type: 'categorical',
        nullPercentage: 0.03,
        categoricalStats: {
          valueCounts: categories.map((cat, i) => ({
            value: cat,
            count: counts[i],
            percentage: counts[i] / sum
          }))
        }
      };
    }
  }, [config.selectedTable, tables]);

  return (
    <div className="p-4 space-y-4">
      <Section
        title="Data Table"
        icon={<Database className="h-5 w-5" />}
        isExpanded={expandedSections.dataTable}
        onToggle={() => toggleSection("dataTable")}
        buttonConfigs={getButtonConfigs("dataTable")}
        warnings={getSectionWarnings("dataTable")}
      >
        <DataTableSelector
          tables={tables}
          selectedTable={config.selectedTable}
          onTableSelect={(tableId: string) => {
            setConfig(prev => ({ ...prev, selectedTable: tableId }));
            setHasUnsavedChanges(prev => ({ ...prev, dataTable: true }));
          }}
        />
      </Section>

      <Section
        title="Configuration"
        icon={<Settings className="h-5 w-5" />}
        isExpanded={expandedSections.config}
        onToggle={() => toggleSection("config")}
        buttonConfigs={getButtonConfigs("config")}
        warnings={getSectionWarnings("config")}
        onExpandComplete={() => handleExpandComplete("config")}
      >
        <ConfigSection
          selectedTable={config.selectedTable}
          columnConfig={config.columnConfig}
          onConfigUpdate={(columnConfig: ColumnConfig) => {
            setConfig(prev => ({ ...prev, columnConfig }));
            setHasUnsavedChanges(prev => ({ ...prev, config: true }));
          }}
          onAutoDetect={(fn) => {
            configAutoDetectRef.current = fn;
          }}
        />
      </Section>

      <Section
        title="Filters"
        icon={<Filter className="h-5 w-5" />}
        isExpanded={expandedSections.filters}
        onToggle={() => toggleSection("filters")}
        buttonConfigs={getButtonConfigs("filters")}
        warnings={getSectionWarnings("filters")}
        onExpandComplete={() => handleExpandComplete("filters")}
      >
        <FiltersSection
          selectedTable={config.selectedTable}
          columnConfig={config.columnConfig}
          timeframeFilter={config.timeframeFilter}
          populationFilters={config.populationFilters}
          onTimeframeUpdate={(timeframeFilter: TimeframeFilter) => {
            setConfig(prev => ({ ...prev, timeframeFilter }));
            setHasUnsavedChanges(prev => ({ ...prev, filters: true }));
          }}
          onPopulationFiltersUpdate={(populationFilters: PopulationFilter[]) => {
            setConfig(prev => ({ ...prev, populationFilters }));
            setHasUnsavedChanges(prev => ({ ...prev, filters: true }));
          }}
          onTimeframeValidityChange={setIsTimeframeValid}
          onAutoDetectTimeframe={(fn) => {
            filtersAutoDetectRef.current = fn;
          }}
        />
      </Section>

      <Section
        title="Analysis"
        icon={<LineChart className="h-5 w-5" />}
        isExpanded={expandedSections.analysis}
        onToggle={() => toggleSection("analysis")}
        buttonConfigs={getButtonConfigs("analysis")}
        warnings={getSectionWarnings("analysis")}
      >
        <AnalysisSection
          selectedTable={config.selectedTable}
          analysisType={analysisType}
          ruleAnalysis={ruleAnalysis}
          metricAnalysis={metricAnalysis}
          onAnalysisTypeChange={(type) => {
            setAnalysisType(type);
            setHasUnsavedChanges(prev => ({ ...prev, analysis: true }));
          }}
          onRuleAnalysisChange={(analysis) => {
            setRuleAnalysis(analysis);
            setHasUnsavedChanges(prev => ({ ...prev, analysis: true }));
          }}
          onMetricAnalysisChange={(columns) => {
            setMetricAnalysis(columns);
            setHasUnsavedChanges(prev => ({ ...prev, analysis: true }));
          }}
          onColumnStatsRequest={handleColumnStatsRequest}
        />
      </Section>
    </div>
  );
}
